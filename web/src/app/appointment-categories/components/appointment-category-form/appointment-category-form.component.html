<!-- <PERSON><PERSON> de Formulário -->
<app-modal
  [title]="title"
  [isOpen]="isVisible"
  (close)="onClose()"
  [showDefaultFooter]="true"
  [fullscreen]="isMobile"
>
  <!-- <PERSON><PERSON><PERSON><PERSON> do <PERSON> -->
  <div class="flex-1 flex flex-col overflow-visible w-full max-w-lg mx-auto">
    <div class="flex-1 overflow-y-auto">
      <form
        id="category-form"
        [formGroup]="form"
        (ngSubmit)="onSubmit()"
        class="p-6 space-y-6 w-full"
      >
        <!-- Nome da Categoria -->
        <div class="space-y-2">
          <label for="name" class="block text-sm font-medium text-gray-700">
            Nome da Categoria *
          </label>
          <input
            id="name"
            type="text"
            formControlName="name"
            placeholder="Ex: Primeira Consulta, Retorno, Limpeza..."
            class="w-full px-4 py-3 text-base border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            [class.border-red-300]="
              form.get('name')?.invalid && form.get('name')?.touched
            "
            [class.focus:ring-red-500]="
              form.get('name')?.invalid && form.get('name')?.touched
            "
          />
          <div
            *ngIf="form.get('name')?.invalid && form.get('name')?.touched"
            class="text-sm text-red-600"
          >
            <span *ngIf="form.get('name')?.errors?.['required']">
              Nome da categoria é obrigatório
            </span>
            <span *ngIf="form.get('name')?.errors?.['maxlength']">
              Nome deve ter no máximo 100 caracteres
            </span>
          </div>
        </div>

        <!-- Cor da Categoria -->
        <div class="space-y-2">
          <app-color-select
            [colors]="availableColors"
            [disabled]="isLoadingColors"
            formControlName="color"
            label="Cor da Categoria"
            placeholder="Selecione uma cor para identificar a categoria"
            [required]="true"
            errorMessage="Selecione uma cor para a categoria"
          ></app-color-select>

          <!-- Loading das cores -->
          <div
            *ngIf="isLoadingColors"
            class="flex items-center justify-center py-4 text-gray-600"
          >
            <div
              class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 mr-2"
            ></div>
            <span class="text-sm">Carregando cores disponíveis...</span>
          </div>
        </div>

        <!-- Descrição -->
        <div class="space-y-2">
          <label
            for="description"
            class="block text-sm font-medium text-gray-700"
          >
            Descrição
            <span class="text-gray-500 font-normal">(opcional)</span>
          </label>
          <textarea
            id="description"
            formControlName="description"
            rows="4"
            placeholder="Descreva o propósito desta categoria de agendamento..."
            class="w-full px-4 py-3 text-base border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-all duration-200"
          ></textarea>
          <p class="text-xs text-gray-500">
            Adicione uma descrição para ajudar a identificar quando usar esta
            categoria.
          </p>
        </div>
      </form>
    </div>
  </div>

  <!-- Footer do Modal -->
  <div
    footer
    class="flex flex-col sm:flex-row justify-end gap-3 px-6 py-4 bg-gray-50 border-t border-gray-200"
  >
    <button
      type="button"
      (click)="onClose()"
      class="w-full sm:w-auto px-6 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
    >
      Cancelar
    </button>
    <button
      type="submit"
      form="category-form"
      [disabled]="form.invalid || isLoading"
      class="w-full sm:w-auto px-6 py-2.5 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
    >
      <span *ngIf="isLoading" class="flex items-center justify-center">
        <div
          class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
        ></div>
        Salvando...
      </span>
      <span *ngIf="!isLoading">
        {{ isEditMode ? "Atualizar Categoria" : "Criar Categoria" }}
      </span>
    </button>
  </div>
</app-modal>
