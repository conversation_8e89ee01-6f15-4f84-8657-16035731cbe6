<!-- <PERSON><PERSON> de Formulário -->
<app-modal
  [title]="title"
  [isOpen]="isVisible"
  (close)="onClose()"
  [showDefaultFooter]="true"
  [fullscreen]="isMobile"
>
  <!-- <PERSON><PERSON><PERSON><PERSON> do <PERSON> -->
  <div class="flex-1 flex flex-col overflow-visible w-full max-w-lg mx-auto">
    <div class="flex-1 overflow-y-auto">
      <form
        id="classification-form"
        [formGroup]="form"
        (ngSubmit)="onSubmit()"
        class="p-6 space-y-6 w-full"
      >
        <!-- Nome da Classificação -->
        <div class="space-y-2">
          <label for="nome" class="block text-sm font-medium text-gray-700">
            Nome da Classificação *
          </label>
          <input
            id="nome"
            type="text"
            formControlName="nome"
            placeholder="Ex: Convênio, Particular, Emergência..."
            class="w-full px-4 py-3 text-base border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            [class.border-red-300]="
              form.get('nome')?.invalid && form.get('nome')?.touched
            "
            [class.focus:ring-red-500]="
              form.get('nome')?.invalid && form.get('nome')?.touched
            "
          />
          <div
            *ngIf="form.get('nome')?.invalid && form.get('nome')?.touched"
            class="text-sm text-red-600"
          >
            <span *ngIf="form.get('nome')?.errors?.['required']">
              Nome da classificação é obrigatório
            </span>
            <span *ngIf="form.get('nome')?.errors?.['maxlength']">
              Nome deve ter no máximo 100 caracteres
            </span>
          </div>
        </div>

        <!-- Cor da Classificação -->
        <div class="space-y-2">
          <app-color-select
            [colors]="availableColors"
            [disabled]="isLoadingColors"
            formControlName="cor"
            label="Cor da Classificação"
            placeholder="Selecione uma cor para identificar a classificação"
            [required]="true"
            errorMessage="Selecione uma cor para a classificação"
          ></app-color-select>

          <!-- Loading das cores -->
          <div
            *ngIf="isLoadingColors"
            class="flex items-center justify-center py-4 text-gray-600"
          >
            <div
              class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 mr-2"
            ></div>
            <span class="text-sm">Carregando cores disponíveis...</span>
          </div>
        </div>

        <!-- Descrição -->
        <div class="space-y-2">
          <label
            for="descricao"
            class="block text-sm font-medium text-gray-700"
          >
            Descrição
            <span class="text-gray-500 font-normal">(opcional)</span>
          </label>
          <textarea
            id="descricao"
            formControlName="descricao"
            rows="4"
            placeholder="Descreva o propósito desta classificação de paciente..."
            class="w-full px-4 py-3 text-base border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-all duration-200"
          ></textarea>
          <p class="text-xs text-gray-500">
            Adicione uma descrição para ajudar a identificar quando usar esta
            classificação.
          </p>
        </div>

        <!-- Status (só aparece quando editando) -->
        <div *ngIf="isEditMode" class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            Status
          </label>
          <div class="flex items-center">
            <input
              type="checkbox"
              id="ativo"
              formControlName="ativo"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label for="ativo" class="ml-2 block text-sm text-gray-700">
              Classificação ativa
            </label>
          </div>
          <p class="text-xs text-gray-500">
            Classificações inativas não aparecerão nas opções de seleção.
          </p>
        </div>
      </form>
    </div>
  </div>

  <!-- Footer do Modal -->
  <div
    footer
    class="flex flex-col sm:flex-row justify-end gap-3 px-6 py-4 bg-gray-50 border-t border-gray-200"
  >
    <button
      type="button"
      (click)="onClose()"
      class="w-full sm:w-auto px-6 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
    >
      Cancelar
    </button>
    <button
      type="submit"
      form="classification-form"
      [disabled]="form.invalid || isLoading"
      class="w-full sm:w-auto px-6 py-2.5 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
    >
      <span *ngIf="isLoading" class="flex items-center justify-center">
        <div
          class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
        ></div>
        Salvando...
      </span>
      <span *ngIf="!isLoading">
        {{ isEditMode ? "Atualizar Classificação" : "Criar Classificação" }}
      </span>
    </button>
  </div>
</app-modal>
