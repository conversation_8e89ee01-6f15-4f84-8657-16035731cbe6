import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  OnChanges,
  SimpleChanges,
  Output,
  HostListener,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { PatientTypeService } from '../../core/services/patient-type.service';
import { PatientType } from '../../core/models/patient-type.model';
import { ModalComponent } from '../../shared/components/modal/modal.component';
import {
  ColorSelectComponent,
  ColorOption,
} from '../../shared/components/color-select/color-select.component';
import { NotificationService } from '../../core/services/notification.service';
import { AppointmentCategoriesService } from '../../appointment-categories/services/appointment-categories.service';

@Component({
  selector: 'app-patient-type-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ModalComponent,
    ColorSelectComponent,
  ],
  templateUrl: './patient-type-form.component.html',
  styleUrls: ['./patient-type-form.component.scss'],
})
export class PatientTypeFormComponent implements OnInit, OnChanges {
  @Input() patientType: PatientType | null = null;
  @Input() isVisible = false;
  @Output() close = new EventEmitter<void>();
  @Output() save = new EventEmitter<PatientType>();

  form: FormGroup;
  availableColors: ColorOption[] = [];
  isLoading = false;
  isLoadingColors = false;
  isMobile = false;

  constructor(
    private fb: FormBuilder,
    private patientTypeService: PatientTypeService,
    private appointmentCategoriesService: AppointmentCategoriesService,
    private notificationService: NotificationService
  ) {
    this.form = this.createForm();
  }

  ngOnInit() {
    this.checkScreenSize();
    this.loadAvailableColors();
  }

  ngOnChanges(changes: SimpleChanges) {
    // Detectar mudanças no patientType
    if (changes['patientType']) {
      if (this.patientType) {
        // Aguardar um ciclo para garantir que o formulário está pronto
        setTimeout(() => this.populateForm(), 0);
      } else {
        // Se não há patientType (novo registro), resetar o formulário
        this.form.reset({
          nome: '',
          descricao: '',
          cor: '',
          ativo: true,
        });
      }
    }

    // Detectar quando o modal se torna visível
    if (changes['isVisible'] && this.isVisible) {
      if (this.patientType) {
        // Modal aberto para edição
        setTimeout(() => this.populateForm(), 0);
      } else {
        // Modal aberto para criação
        this.form.reset({
          nome: '',
          descricao: '',
          cor: '',
          ativo: true,
        });
      }
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenSize();
  }

  private checkScreenSize() {
    this.isMobile = window.innerWidth < 768; // Tailwind md breakpoint
  }

  private createForm(): FormGroup {
    return this.fb.group({
      nome: ['', [Validators.required, Validators.maxLength(100)]],
      descricao: [''],
      cor: ['', [Validators.required, Validators.pattern(/^#[0-9A-Fa-f]{6}$/)]],
      ativo: [true],
    });
  }

  private populateForm() {
    if (this.patientType) {
      const formData = {
        nome: this.patientType.nome,
        descricao: this.patientType.descricao || '',
        cor: this.patientType.cor,
        ativo: this.patientType.ativo,
      };

      this.form.patchValue(formData);

      // Se as cores já foram carregadas, forçar atualização do componente de cor
      if (this.availableColors.length > 0) {
        this.updateColorSelection();
      }
    }
  }

  private updateColorSelection() {
    if (this.patientType && this.form.get('cor')?.value) {
      const colorValue = this.form.get('cor')?.value;
      // Verificar se a cor existe nas opções disponíveis
      const matchingColor = this.availableColors.find(
        (c) => c.hex === colorValue
      );
      if (matchingColor) {
        // Força a atualização do componente de seleção de cor
        setTimeout(() => {
          this.form.get('cor')?.setValue(colorValue);
          this.form.get('cor')?.updateValueAndValidity();
        }, 100);
      }
    }
  }

  private loadAvailableColors() {
    this.isLoadingColors = true;
    this.appointmentCategoriesService.getAvailableColors().subscribe({
      next: (colors) => {
        // Converter AppColor para ColorOption com IDs únicos
        this.availableColors = colors.map((color, index) => ({
          id: index + 1,
          name: color.name.replace(/_/g, ' '), // Substituir underscores por espaços
          hex: color.hex,
        }));
        this.isLoadingColors = false;

        // Se estamos editando, garantir que a cor selecionada seja encontrada
        if (this.patientType) {
          this.updateColorSelection();
        }
      },
      error: (error) => {
        console.error('Erro ao carregar cores:', error);
        this.isLoadingColors = false;
        this.notificationService.error(
          'Erro ao carregar as cores disponíveis. Tente recarregar a página.'
        );
      },
    });
  }

  onSubmit() {
    if (this.form.valid && !this.isLoading) {
      this.isLoading = true;
      const formData = { ...this.form.value };

      // Para criação, sempre definir ativo como true se não for modo de edição
      if (!this.patientType) {
        formData.ativo = true;
      }

      const request = this.patientType
        ? this.patientTypeService.updatePatientType(
            this.patientType.id,
            formData
          )
        : this.patientTypeService.createPatientType(formData);

      request.subscribe({
        next: (result) => {
          const action = this.isEditMode ? 'atualizada' : 'criada';
          this.notificationService.success(
            `Classificação ${action} com sucesso!`
          );
          this.save.emit(result);
          this.onClose();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Erro ao salvar classificação:', error);
          this.isLoading = false;

          // Determina a mensagem de erro baseada no tipo de operação
          const action = this.isEditMode ? 'atualizar' : 'criar';
          let errorMessage = `Erro ao ${action} a classificação. Tente novamente.`;

          // Verifica se é erro de nome duplicado
          if (
            error.status === 409 ||
            error.error?.message?.includes('já existe')
          ) {
            errorMessage =
              'Já existe uma classificação com este nome. Escolha um nome diferente.';
          }

          this.notificationService.error(errorMessage);
        },
      });
    }
  }

  onClose() {
    this.form.reset();
    this.patientType = null;
    this.close.emit();
  }

  get isEditMode(): boolean {
    return !!this.patientType;
  }

  get title(): string {
    return this.isEditMode ? 'Editar Classificação' : 'Nova Classificação';
  }
}
