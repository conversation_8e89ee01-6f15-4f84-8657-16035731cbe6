<div class="bg-white shadow rounded-lg p-6">
  <!-- Header -->
  <div
    class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6"
  >
    <div>
      <h1 class="text-2xl font-bold text-gray-900">
        Classificações de Paciente
      </h1>
      <p class="mt-1 text-sm text-gray-600">
        <PERSON><PERSON><PERSON><PERSON> as classificações utilizadas para categorizar os pacientes da
        clínica
      </p>
    </div>
    <button
      (click)="openNewPatientTypeModal()"
      class="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
    >
      <svg
        class="w-5 h-5 mr-2"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 6v6m0 0v6m0-6h6m-6 0H6"
        ></path>
      </svg>
      Nova Classificação
    </button>
  </div>

  <!-- Filtros -->
  <div class="bg-gray-50 rounded-lg p-4 mb-6">
    <form [formGroup]="filterForm" class="flex flex-col sm:flex-row gap-4">
      <!-- Filtro por Nome (maior largura) -->
      <div class="flex-1">
        <label
          for="filterName"
          class="block text-sm font-medium text-gray-700 mb-2"
        >
          Buscar por nome
        </label>
        <div class="relative">
          <input
            id="filterName"
            type="text"
            formControlName="name"
            placeholder="Digite o nome da classificação..."
            class="w-full pl-10 pr-4 py-2.5 text-base border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
          />
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg
              class="h-5 w-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              ></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- Filtro por Status -->
      <div class="w-full sm:w-48">
        <label
          for="filterStatus"
          class="block text-sm font-medium text-gray-700 mb-2"
        >
          Status
        </label>
        <select
          id="filterStatus"
          formControlName="isActive"
          class="w-full px-3 py-2.5 text-base border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
        >
          <option [ngValue]="null">Todos os status</option>
          <option [ngValue]="true">Ativos</option>
          <option [ngValue]="false">Inativos</option>
        </select>
      </div>

      <!-- Botão Limpar Filtros (só aparece se tiver filtros) -->
      <div *ngIf="hasActiveFilters" class="flex items-end">
        <button
          type="button"
          (click)="clearFilters()"
          class="px-4 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center"
          title="Limpar todos os filtros"
        >
          <svg
            class="w-4 h-4 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
          Limpar
        </button>
      </div>
    </form>
  </div>

  <!-- Loading -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-12">
    <div
      class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"
    ></div>
    <span class="ml-3 text-gray-600">Carregando classificações...</span>
  </div>

  <!-- Tabela -->
  <div *ngIf="!isLoading" class="bg-white shadow-sm rounded-lg overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
              (click)="onSort('nome')"
            >
              <div class="flex items-center space-x-1">
                <span>Nome</span>
                <svg
                  *ngIf="sortField === 'nome'"
                  class="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    *ngIf="sortDirection === 'asc'"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5 15l7-7 7 7"
                  ></path>
                  <path
                    *ngIf="sortDirection === 'desc'"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
              </div>
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Cor
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Descrição
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
              (click)="onSort('ativo')"
            >
              <div class="flex items-center space-x-1">
                <span>Status</span>
                <svg
                  *ngIf="sortField === 'ativo'"
                  class="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    *ngIf="sortDirection === 'asc'"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5 15l7-7 7 7"
                  ></path>
                  <path
                    *ngIf="sortDirection === 'desc'"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
              </div>
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Ações
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr
            *ngFor="let patientType of filteredPatientTypes"
            class="hover:bg-gray-50 transition-colors"
          >
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900">
                {{ patientType.nome }}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <app-color-circle
                [color]="patientType.cor"
                [title]="patientType.cor"
              ></app-color-circle>
            </td>
            <td class="px-6 py-4">
              <div
                class="text-sm text-gray-600 max-w-xs truncate"
                [title]="patientType.descricao"
              >
                {{ patientType.descricao || "-" }}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                [class.bg-green-100]="patientType.ativo"
                [class.text-green-800]="patientType.ativo"
                [class.bg-red-100]="!patientType.ativo"
                [class.text-red-800]="!patientType.ativo"
              >
                {{ patientType.ativo ? "Ativo" : "Inativo" }}
              </span>
            </td>
            <td
              class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
            >
              <div class="flex items-center justify-end space-x-2">
                <button
                  (click)="openEditPatientTypeModal(patientType)"
                  class="text-blue-600 hover:text-blue-900 transition-colors"
                  title="Editar classificação"
                >
                  <svg
                    class="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    ></path>
                  </svg>
                </button>
                <app-toggle-switch
                  [checked]="patientType.ativo"
                  [disabled]="
                    isTogglingStatus && patientTypeToToggle?.id === patientType.id
                  "
                  [showLabel]="false"
                  size="sm"
                  color="green"
                  (change)="onToggleStatus(patientType)"
                ></app-toggle-switch>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div *ngIf="filteredPatientTypes.length === 0" class="text-center py-12">
      <svg
        class="mx-auto h-12 w-12 text-gray-400"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
        ></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">
        Nenhuma classificação encontrada
      </h3>
      <p class="mt-1 text-sm text-gray-500">
        {{
          filterForm.value.name || filterForm.value.isActive !== null
            ? "Tente ajustar os filtros ou"
            : "Comece"
        }}
        criando uma nova classificação.
      </p>
      <div class="mt-6">
        <button
          (click)="openNewPatientTypeModal()"
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
        >
          <svg
            class="w-5 h-5 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            ></path>
          </svg>
          Nova Classificação
        </button>
      </div>
    </div>
  </div>
        </tbody>
      </table>
    </div>

    <!-- Paginação Moderna -->
    <div
      *ngIf="totalItems > 0"
      class="px-6 py-5 bg-white border-t border-gray-100 shadow-inner"
    >
      <div
        class="flex flex-col md:flex-row md:items-center md:justify-between gap-4"
      >
        <!-- Informações de paginação e seletor de itens por página -->
        <div class="flex flex-wrap items-center text-sm text-gray-600">
          <div
            class="flex items-center bg-gray-50 px-3 py-1.5 rounded-md shadow-sm"
          >
            <span>Mostrando</span>
            <span class="font-medium mx-1 text-blue-600">{{
              (currentPage - 1) * itemsPerPage + 1
            }}</span>
            <span>-</span>
            <span class="font-medium mx-1 text-blue-600">{{
              currentPage * itemsPerPage > totalItems
                ? totalItems
                : currentPage * itemsPerPage
            }}</span>
            <span>de</span>
            <span class="font-medium mx-1 text-blue-600">{{ totalItems }}</span>
          </div>

          <!-- Seletor de itens por página -->
          <div class="ml-3 flex items-center">
            <label for="itemsPerPage" class="mr-2 text-sm text-gray-500"
              >Itens por página:</label
            >
            <div class="relative">
              <select
                id="itemsPerPage"
                [(ngModel)]="itemsPerPage"
                (change)="changeItemsPerPage()"
                class="appearance-none bg-white border border-gray-200 rounded-md pl-3 pr-8 py-1.5 text-sm shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              >
                <option [value]="6">6</option>
                <option [value]="10">10</option>
                <option [value]="20">20</option>
                <option [value]="50">50</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Controles de paginação -->
        <div class="flex items-center pagination-controls">
          <div class="flex rounded-lg shadow-sm overflow-hidden">
            <!-- Primeira página -->
            <button
              (click)="firstPage()"
              [disabled]="currentPage === 1 || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === 1 || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Primeira página"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M11 19l-7-7 7-7m8 14l-7-7 7-7"
                />
              </svg>
            </button>

            <!-- Página anterior -->
            <button
              (click)="previousPage()"
              [disabled]="currentPage === 1 || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === 1 || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Página anterior"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            <!-- Números de página -->
            <ng-container *ngFor="let page of getPageNumbers()">
              <ng-container *ngIf="page !== '...'">
                <button
                  (click)="goToPage(page)"
                  [disabled]="isLoading"
                  class="relative inline-flex items-center justify-center h-9 min-w-[2.25rem] text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  [ngClass]="
                    currentPage === page
                      ? 'bg-blue-500 text-white font-medium border-blue-500 hover:bg-blue-600'
                      : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
                  "
                >
                  {{ page }}
                </button>
              </ng-container>
              <div
                *ngIf="page === '...'"
                class="relative inline-flex items-center justify-center h-9 min-w-[2.25rem] text-sm text-gray-500 border-r border-gray-200 bg-white"
              >
                ...
              </div>
            </ng-container>

            <!-- Próxima página -->
            <button
              (click)="nextPage()"
              [disabled]="currentPage === totalPages || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out border-r border-gray-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === totalPages || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Próxima página"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>

            <!-- Última página -->
            <button
              (click)="lastPage()"
              [disabled]="currentPage === totalPages || isLoading"
              class="relative inline-flex items-center justify-center h-9 w-9 text-sm transition-colors duration-150 ease-in-out focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              [ngClass]="
                currentPage === totalPages || isLoading
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600'
              "
              aria-label="Última página"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 5l7 7-7 7M5 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal de Formulário -->
<app-patient-type-form
  [isVisible]="isModalOpen"
  [patientType]="selectedPatientType"
  (close)="closeModal()"
  (save)="onPatientTypeSaved($event)"
></app-patient-type-form>

<!-- Modal de Confirmação de Status -->
<app-confirmation-dialog
  [isOpen]="showStatusConfirmation"
  [title]="statusConfirmationTitle"
  [message]="statusConfirmationMessage"
  [confirmButtonText]="statusConfirmationButtonText"
  cancelButtonText="Cancelar"
  [confirmButtonClass]="
    patientTypeToToggle?.ativo
      ? 'bg-orange-600 hover:bg-orange-700'
      : 'bg-green-600 hover:bg-green-700'
  "
  [type]="patientTypeToToggle?.ativo ? 'warning' : 'info'"
  [isLoading]="isTogglingStatus"
  [loadingText]="statusConfirmationLoadingText"
  (confirm)="confirmToggleStatus()"
  (cancel)="closeStatusConfirmation()"
></app-confirmation-dialog>
