import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { debounceTime, distinctUntilChanged, finalize } from 'rxjs/operators';
import { PatientType } from '../../core/models/patient-type.model';
import { PatientTypeService } from '../../core/services/patient-type.service';
import { PaginatedResponse } from '../../core/models/pagination.model';
import { PatientTypeFormComponent } from '../patient-type-form/patient-type-form.component';
import { ColorCircleComponent } from '../../shared/components/color-circle/color-circle.component';
import { ToggleSwitchComponent } from '../../shared/components/toggle-switch/toggle-switch.component';
import { ConfirmationDialogComponent } from '../../shared/components/confirmation-dialog/confirmation-dialog.component';
import { NotificationService } from '../../core/services/notification.service';

@Component({
  selector: 'app-patient-type-list',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    PatientTypeFormComponent,
    ColorCircleComponent,
    ToggleSwitchComponent,
    ConfirmationDialogComponent,
  ],
  templateUrl: './patient-type-list.component.html',
  styleUrls: ['./patient-type-list.component.scss'],
})
export class PatientTypeListComponent implements OnInit {
  patientTypes: PatientType[] = [];
  filteredPatientTypes: PatientType[] = [];
  isLoading = true;
  error: string | null = null;

  // Filtros
  filterForm: FormGroup;

  // Paginação
  currentPage: number = 1;
  itemsPerPage: number = 10;
  totalItems: number = 0;
  totalPages: number = 0;

  // Ordenação
  sortField: string = 'nome';
  sortDirection: 'asc' | 'desc' = 'asc';

  // Modal
  isModalOpen = false;
  selectedPatientType: PatientType | null = null;

  // Status toggle
  showStatusConfirmation = false;
  patientTypeToToggle: PatientType | null = null;
  isTogglingStatus = false;

  constructor(
    private fb: FormBuilder,
    private patientTypeService: PatientTypeService,
    private notificationService: NotificationService
  ) {
    this.filterForm = this.createFilterForm();
  }

  ngOnInit(): void {
    this.setupFilterSubscription();
    this.loadPatientTypes();
  }

  private createFilterForm(): FormGroup {
    return this.fb.group({
      name: [''],
      isActive: [null], // null = todos, true = ativos, false = inativos
    });
  }

  private setupFilterSubscription() {
    this.filterForm.valueChanges
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe(() => {
        this.currentPage = 1;
        this.loadPatientTypes();
      });
  }

  get hasActiveFilters(): boolean {
    const formValue = this.filterForm.value;
    return !!(formValue.name?.trim() || formValue.isActive !== null);
  }

  private loadPatientTypes() {
    this.isLoading = true;
    this.error = null;

    const filters: any = {
      page: this.currentPage,
      limit: this.itemsPerPage,
    };

    const formValue = this.filterForm.value;
    if (formValue.name?.trim()) {
      filters.search = formValue.name.trim();
    }
    if (formValue.isActive !== null) {
      filters.ativo = formValue.isActive;
    }

    this.patientTypeService
      .getPatientTypes(
        filters.page,
        filters.limit,
        filters.search,
        filters.ativo
      )
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: (response: PaginatedResponse<PatientType>) => {
          this.patientTypes = response.data;
          this.filteredPatientTypes = [...this.patientTypes];
          this.totalItems = response.total;
          this.currentPage = response.page;
          this.itemsPerPage = response.limit;
          this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        },
        error: (error) => {
          console.error('Erro ao carregar classificações:', error);
          this.error =
            'Erro ao carregar as classificações. Tente recarregar a página.';
          this.notificationService.error(
            'Erro ao carregar as classificações de paciente.'
          );
        },
      });
  }

  clearFilters() {
    this.filterForm.reset({
      name: '',
      isActive: null,
    });
  }

  onSort(field: string) {
    if (this.sortField === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = field;
      this.sortDirection = 'asc';
    }
    this.loadPatientTypes();
  }

  openNewPatientTypeModal(): void {
    this.selectedPatientType = null;
    this.isModalOpen = true;
  }

  openEditPatientTypeModal(patientType: PatientType): void {
    this.selectedPatientType = patientType;
    this.isModalOpen = true;
  }

  closeModal(): void {
    this.isModalOpen = false;
    this.selectedPatientType = null;
  }

  onPatientTypeSaved(patientType: PatientType): void {
    this.closeModal();
    this.loadPatientTypes();
    this.notificationService.success(
      `Classificação ${
        this.selectedPatientType ? 'atualizada' : 'criada'
      } com sucesso!`
    );
  }

  onToggleStatus(patientType: PatientType) {
    this.patientTypeToToggle = patientType;
    this.showStatusConfirmation = true;
  }

  get statusConfirmationTitle(): string {
    return this.patientTypeToToggle?.ativo
      ? 'Desativar Classificação'
      : 'Ativar Classificação';
  }

  get statusConfirmationMessage(): string {
    const action = this.patientTypeToToggle?.ativo ? 'desativar' : 'ativar';
    return `Tem certeza que deseja ${action} a classificação "${this.patientTypeToToggle?.nome}"?`;
  }

  get statusConfirmationButtonText(): string {
    return this.patientTypeToToggle?.ativo ? 'Desativar' : 'Ativar';
  }

  get statusConfirmationLoadingText(): string {
    return this.patientTypeToToggle?.ativo ? 'Desativando...' : 'Ativando...';
  }

  confirmToggleStatus() {
    if (!this.patientTypeToToggle) return;

    this.isTogglingStatus = true;
    const newStatus = !this.patientTypeToToggle.ativo;

    this.patientTypeService
      .updatePatientType(this.patientTypeToToggle.id, { ativo: newStatus })
      .pipe(finalize(() => (this.isTogglingStatus = false)))
      .subscribe({
        next: (updatedPatientType) => {
          // Atualizar o item na lista local
          const index = this.patientTypes.findIndex(
            (pt) => pt.id === updatedPatientType.id
          );
          if (index !== -1) {
            this.patientTypes[index] = updatedPatientType;
            this.filteredPatientTypes = [...this.patientTypes];
          }

          this.closeStatusConfirmation();
          this.notificationService.success(
            `Classificação ${newStatus ? 'ativada' : 'desativada'} com sucesso!`
          );
        },
        error: (error) => {
          console.error('Erro ao alterar status:', error);
          this.closeStatusConfirmation();
          this.notificationService.error(
            'Erro ao alterar o status da classificação. Tente novamente.'
          );
        },
      });
  }

  closeStatusConfirmation() {
    this.showStatusConfirmation = false;
    this.patientTypeToToggle = null;
    this.isTogglingStatus = false;
  }

  /**
   * Gera um array com os números de página a serem exibidos na paginação
   */
  getPageNumbers(): (number | string)[] {
    const pages: (number | string)[] = [];
    const maxPagesToShow = 5;

    if (this.totalPages <= maxPagesToShow) {
      // Se houver poucas páginas, mostrar todas
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Sempre mostrar a primeira página
      pages.push(1);

      // Calcular o intervalo de páginas a mostrar
      let startPage = Math.max(2, this.currentPage - 1);
      let endPage = Math.min(this.totalPages - 1, this.currentPage + 1);

      // Ajustar para mostrar sempre 3 páginas no meio
      if (startPage === 2) {
        endPage = Math.min(this.totalPages - 1, 4);
      } else if (endPage === this.totalPages - 1) {
        startPage = Math.max(2, this.totalPages - 3);
      }

      // Adicionar elipses se necessário
      if (startPage > 2) {
        pages.push('...');
      }

      // Adicionar páginas do meio
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      // Adicionar elipses se necessário
      if (endPage < this.totalPages - 1) {
        pages.push('...');
      }

      // Sempre mostrar a última página
      pages.push(this.totalPages);
    }

    return pages;
  }

  changeItemsPerPage(): void {
    this.currentPage = 1;
    this.loadPatientTypes();
  }

  goToPage(page: number | string): void {
    if (typeof page === 'string') {
      return;
    }

    if (
      page < 1 ||
      page > this.totalPages ||
      page === this.currentPage ||
      this.isLoading
    ) {
      return;
    }
    this.currentPage = page;
    this.loadPatientTypes();
  }

  previousPage(): void {
    if (this.currentPage > 1 && !this.isLoading) {
      this.goToPage(this.currentPage - 1);
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages && !this.isLoading) {
      this.goToPage(this.currentPage + 1);
    }
  }

  firstPage(): void {
    if (!this.isLoading) {
      this.goToPage(1);
    }
  }

  lastPage(): void {
    if (!this.isLoading) {
      this.goToPage(this.totalPages);
    }
  }
}
