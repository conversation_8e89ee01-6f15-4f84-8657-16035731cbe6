<div class="h-screen flex bg-gray-50 rounded overflow-hidden shadow">
  <!-- Sidebar à esquerda - Mostrar apenas quando sidebar principal estiver colapsado e não for mobile -->
  <div
    *ngIf="shouldShowFilterInlineResponsive"
    class="w-64 lg:w-64 md:w-56 sm:w-48 bg-white shadow-lg border-r border-gray-200 flex-col hidden sm:flex"
  >
    <!-- Header do Sidebar -->
    <div class="p-4 border-b border-gray-200">
      <h2 class="text-lg font-semibold text-gray-900">Agenda</h2>
      <button
        (click)="openNewAppointmentSheet()"
        class="mt-3 w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 mr-2"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
            clip-rule="evenodd"
          />
        </svg>
        Novo Agendamento
      </button>
    </div>

    <!-- Conteúdo do Sidebar -->
    <div class="flex-1 overflow-y-auto sidebar-content">
      <!-- Seção de Filtros -->
      <div class="p-4">
        <!-- Lista de Dentistas -->
        <div class="mb-4">
          <h4
            class="text-xs font-medium text-gray-700 mb-3 uppercase tracking-wide"
          >
            Disponíveis na área de serviço
          </h4>

          <!-- Botões de controle -->
          <div
            class="flex items-center w-full justify-between mb-3 p-2 bg-gray-50 rounded-lg border border-gray-200"
          >
            <div class="flex items-center space-x-2 w-full">
              <button
                (click)="selectAllDentists()"
                [disabled]="areAllDentistsSelected()"
                class="px-3 w-full py-1.5 text-xs font-medium bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 hover:text-blue-800 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-1"
                title="Selecionar todos os dentistas"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-3 w-3"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span>Todos</span>
              </button>

              <button
                (click)="deselectAllDentists()"
                [disabled]="areNoDentistsSelected()"
                class="px-3 w-full py-1.5 text-xs font-medium bg-red-100 text-red-700 rounded-md hover:bg-red-200 hover:text-red-800 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-1"
                title="Desmarcar todos os dentistas"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-3 w-3"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span>Nenhum</span>
              </button>
            </div>
          </div>

          <!-- Loading state -->
          <div *ngIf="isLoading" class="space-y-2">
            <div class="animate-pulse flex items-center space-x-3 p-2">
              <div class="w-3 h-3 bg-gray-300 rounded-full"></div>
              <div class="h-4 bg-gray-300 rounded flex-1"></div>
            </div>
            <div class="animate-pulse flex items-center space-x-3 p-2">
              <div class="w-3 h-3 bg-gray-300 rounded-full"></div>
              <div class="h-4 bg-gray-300 rounded flex-1"></div>
            </div>
          </div>

          <!-- Lista de dentistas -->
          <div *ngIf="!isLoading" class="space-y-1">
            <div
              *ngFor="let dentist of dentists"
              class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-md cursor-pointer"
              (click)="onDentistToggle(dentist.id)"
            >
              <input
                type="checkbox"
                [checked]="selectedDentists[dentist.id]"
                (click)="$event.stopPropagation()"
                (change)="onDentistToggle(dentist.id)"
                class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <div class="flex items-center space-x-2 flex-1">
                <div
                  [class]="'w-3 h-3 rounded-full ' + getDentistColor(dentist)"
                ></div>
                <span class="text-sm text-gray-900 font-medium">{{
                  dentist.name
                }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Fora da área de serviço -->
        <div class="mb-4">
          <h4
            class="text-xs font-medium text-gray-700 mb-2 uppercase tracking-wide"
          >
            Fora da área de serviço
          </h4>
          <div class="text-xs text-gray-500 italic">
            Nenhum dentista fora da área
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Área principal à direita -->
  <div class="flex-1 flex flex-col">
    <!-- Header da área principal -->
    <div class="bg-white shadow-sm border-b border-gray-200 p-4 lg:p-6">
      <div
        class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0"
      >
        <div class="flex items-center space-x-2 lg:space-x-4">
          <!-- Botão de filtros - Mostrar baseado na responsividade -->
          <div
            *ngIf="shouldShowFilterButtonResponsive"
            class="filter-dropdown-container relative"
          >
            <button
              (click)="toggleFilterDropdown()"
              class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                  clip-rule="evenodd"
                />
              </svg>
              <span>Filtros</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 transition-transform"
                [class.rotate-180]="isFilterDropdownOpen"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>

            <!-- Dropdown de filtros -->
            <div
              *ngIf="isFilterDropdownOpen"
              class="filter-dropdown absolute top-full left-0 mt-2 w-80 sm:w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50 max-w-[calc(100vw-2rem)] mx-2 sm:mx-0"
            >
              <!-- Header do dropdown -->
              <div class="p-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold text-gray-900">Filtros</h3>
                  <button
                    (click)="closeFilterDropdown()"
                    class="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <!-- Conteúdo do dropdown -->
              <div class="p-4">
                <!-- Buscar agendamento existente -->

                <!-- Lista de Dentistas -->
                <div class="mb-4">
                  <h4
                    class="text-xs font-medium text-gray-700 mb-3 uppercase tracking-wide"
                  >
                    Disponíveis na área de serviço
                  </h4>

                  <!-- Botões de controle -->
                  <div
                    class="flex items-center w-full justify-between mb-3 p-2 bg-gray-50 rounded-lg border border-gray-200"
                  >
                    <div class="flex items-center space-x-2 w-full">
                      <button
                        (click)="selectAllDentists()"
                        [disabled]="areAllDentistsSelected()"
                        class="px-3 w-full py-1.5 text-xs font-medium bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 hover:text-blue-800 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-1"
                        title="Selecionar todos os dentistas"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3 w-3"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clip-rule="evenodd"
                          />
                        </svg>
                        <span>Todos</span>
                      </button>

                      <button
                        (click)="deselectAllDentists()"
                        [disabled]="areNoDentistsSelected()"
                        class="px-3 w-full py-1.5 text-xs font-medium bg-red-100 text-red-700 rounded-md hover:bg-red-200 hover:text-red-800 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-1"
                        title="Desmarcar todos os dentistas"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3 w-3"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clip-rule="evenodd"
                          />
                        </svg>
                        <span>Nenhum</span>
                      </button>
                    </div>
                  </div>

                  <!-- Loading state -->
                  <div *ngIf="isLoading" class="space-y-2">
                    <div class="animate-pulse flex items-center space-x-3 p-2">
                      <div class="w-3 h-3 bg-gray-300 rounded-full"></div>
                      <div class="h-4 bg-gray-300 rounded flex-1"></div>
                    </div>
                    <div class="animate-pulse flex items-center space-x-3 p-2">
                      <div class="w-3 h-3 bg-gray-300 rounded-full"></div>
                      <div class="h-4 bg-gray-300 rounded flex-1"></div>
                    </div>
                  </div>

                  <!-- Lista de dentistas -->
                  <div *ngIf="!isLoading" class="space-y-1">
                    <div
                      *ngFor="let dentist of dentists"
                      class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-md cursor-pointer"
                      (click)="onDentistToggle(dentist.id)"
                    >
                      <input
                        type="checkbox"
                        [checked]="selectedDentists[dentist.id]"
                        (click)="$event.stopPropagation()"
                        (change)="onDentistToggle(dentist.id)"
                        class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <div class="flex items-center space-x-2 flex-1">
                        <div
                          [class]="
                            'w-3 h-3 rounded-full ' + getDentistColor(dentist)
                          "
                        ></div>
                        <span class="text-sm text-gray-900 font-medium">{{
                          dentist.name
                        }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Fora da área de serviço -->
                <div class="mb-4">
                  <h4
                    class="text-xs font-medium text-gray-700 mb-2 uppercase tracking-wide"
                  >
                    Fora da área de serviço
                  </h4>
                  <div class="text-xs text-gray-500 italic">
                    Nenhum dentista fora da área
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="min-w-0 flex-1">
            <h1 class="text-xl lg:text-2xl font-bold text-gray-900 truncate">
              Agenda v2
            </h1>
            <p class="text-gray-600 mt-1 text-sm lg:text-base hidden sm:block">
              Gerencie seus agendamentos de forma eficiente
            </p>
          </div>
        </div>

        <!-- Controles de visualização -->
        <div
          class="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4 w-full lg:w-auto"
        >
          <!-- Seletor de visualização -->
          <div class="flex bg-gray-100 rounded-lg p-1 w-full sm:w-auto">
            <button
              (click)="setViewMode('day')"
              [class]="
                viewMode === 'day'
                  ? 'px-2 lg:px-3 py-1 text-xs lg:text-sm font-medium bg-white text-gray-900 rounded-md shadow-sm flex-1 sm:flex-none'
                  : 'px-2 lg:px-3 py-1 text-xs lg:text-sm font-medium text-gray-600 hover:text-gray-900 flex-1 sm:flex-none'
              "
            >
              Dia
            </button>
            <button
              (click)="setViewMode('week')"
              [class]="
                viewMode === 'week'
                  ? 'px-2 lg:px-3 py-1 text-xs lg:text-sm font-medium bg-white text-gray-900 rounded-md shadow-sm flex-1 sm:flex-none'
                  : 'px-2 lg:px-3 py-1 text-xs lg:text-sm font-medium text-gray-600 hover:text-gray-900 flex-1 sm:flex-none'
              "
            >
              Semana
            </button>
            <button
              (click)="setViewMode('month')"
              [class]="
                viewMode === 'month'
                  ? 'px-2 lg:px-3 py-1 text-xs lg:text-sm font-medium bg-white text-gray-900 rounded-md shadow-sm flex-1 sm:flex-none'
                  : 'px-2 lg:px-3 py-1 text-xs lg:text-sm font-medium text-gray-600 hover:text-gray-900 flex-1 sm:flex-none'
              "
            >
              Mês
            </button>
          </div>

          <!-- Navegação de data -->
          <div
            class="flex items-center justify-between sm:justify-start space-x-2 w-full sm:w-auto"
          >
            <button
              (click)="goToPreviousPeriod()"
              class="p-1.5 lg:p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 lg:h-5 lg:w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>

            <span
              class="text-sm lg:text-lg font-semibold text-gray-900 min-w-0 flex-1 sm:min-w-[150px] lg:min-w-[200px] text-center truncate px-2"
            >
              {{ getCurrentPeriodLabel() }}
            </span>

            <button
              (click)="goToNextPeriod()"
              class="p-1.5 lg:p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 lg:h-5 lg:w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </div>

          <!-- Botões de ação -->
          <div class="flex items-center space-x-2 w-full sm:w-auto">
            <!-- Botão hoje -->
            <button
              (click)="goToToday()"
              class="px-3 lg:px-4 py-2 text-xs lg:text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 flex-1 sm:flex-none"
            >
              Hoje
            </button>

            <!-- Botão Novo Agendamento - Mostrar apenas quando sidebar não estiver colapsado -->
            <button
              *ngIf="!shouldShowFilterInlineResponsive"
              (click)="openNewAppointmentSheet()"
              class="px-3 lg:px-4 py-2 bg-blue-600 text-white text-xs lg:text-sm font-medium rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center space-x-1 lg:space-x-2 flex-1 sm:flex-none"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-3 w-3 lg:h-4 lg:w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                  clip-rule="evenodd"
                />
              </svg>
              <span class="hidden sm:inline">Novo Agendamento</span>
              <span class="sm:hidden">Novo</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Conteúdo principal -->
    <div class="flex-1 p-2 lg:p-6 overflow-hidden">
      <!-- Grid da agenda - Visualização Diária -->
      <div
        *ngIf="viewMode === 'day'"
        class="bg-white rounded-lg shadow-sm border border-gray-200 h-full overflow-hidden schedule-grid"
      >
        <div class="h-full overflow-auto">
          <div
            class="grid"
            [style.grid-template-columns]="getDayGridColumns()"
            [style.min-width]="getMinGridWidth() + 'px'"
            *ngIf="getSelectedDentistsData().length > 0"
          >
            <!-- Cabeçalho com horários e dentistas -->
            <div
              class="sticky top-0 bg-gray-50 border-b border-gray-200 p-1 lg:p-3 text-xs font-medium text-gray-500 text-center z-20"
            >
              <span class="hidden sm:inline">Horário</span>
              <span class="sm:hidden">Hr</span>
            </div>

            <!-- Cabeçalhos dos dentistas -->
            <div
              *ngFor="let dentist of getSelectedDentistsData()"
              class="sticky top-0 dentist-header border-b border-l border-gray-200 p-1 lg:p-3 text-center z-20"
            >
              <div
                class="flex flex-col lg:flex-row items-center justify-center space-y-1 lg:space-y-0 lg:space-x-2"
              >
                <div
                  class="w-2 h-2 lg:w-3 lg:h-3 rounded-full"
                  [ngClass]="getDentistColor(dentist)"
                ></div>
                <span
                  class="text-xs lg:text-sm font-medium text-gray-900 truncate max-w-full"
                  >{{ dentist.name }}</span
                >
              </div>
            </div>

            <!-- Linhas de horário -->
            <ng-container *ngFor="let timeSlot of timeSlots; let i = index">
              <!-- Coluna de horário -->
              <div
                class="border-b border-gray-100 p-1 lg:p-2 text-xs text-gray-500 text-center bg-gray-50 sticky left-0 z-10"
                [class.border-r]="true"
                [class.border-gray-300]="timeSlot.minute === 0"
                [class.bg-gray-100]="timeSlot.minute === 0"
              >
                <span
                  class="font-medium text-xs lg:text-sm"
                  [class.text-gray-900]="timeSlot.minute === 0"
                  >{{ timeSlot.time }}</span
                >
              </div>

              <!-- Células para cada dentista -->
              <div
                *ngFor="let dentist of getSelectedDentistsData()"
                class="relative min-h-[30px] lg:min-h-[40px] time-slot"
                [class.border-b]="
                  !isSlotOccupiedByAppointment(dentist.id, timeSlot.time)
                "
                [class.border-l]="
                  !isSlotOccupiedByAppointment(dentist.id, timeSlot.time)
                "
                [class.border-t]="
                  !isSlotOccupiedByAppointment(dentist.id, timeSlot.time)
                "
                [class.border-gray-100]="
                  !isSlotOccupiedByAppointment(dentist.id, timeSlot.time)
                "
                [class.border-gray-300]="
                  timeSlot.minute === 0 &&
                  !isSlotOccupiedByAppointment(dentist.id, timeSlot.time)
                "
                [class.occupied-slot]="
                  isSlotOccupiedByAppointment(dentist.id, timeSlot.time)
                "
                [ngClass]="getSlotOccupiedClasses(dentist.id, timeSlot.time)"
              >
                <!-- Card de agendamento se existir (apenas no slot de início) -->
                <div
                  *ngIf="
                    getAppointmentForSlot(
                      dentist.id,
                      timeSlot.time
                    ) as appointment
                  "
                  class="absolute top-0 bottom-0 left-1 right-1 lg:left-2 lg:right-2 rounded-md shadow-lg border-l-4 p-2 lg:p-3 text-xs z-10 appointment-card cursor-pointer transition-all duration-200"
                  [style.height.px]="getAppointmentHeight(appointment)"
                  [style.background-color]="
                    appointment.appointmentCategory?.color ||
                    getPatientTypeConfig(appointment.patientType).bgColor
                  "
                  [ngClass]="[
                    'border-l-' + getStatusBorderColor(appointment.status)
                  ]"
                  [title]="getAppointmentTooltip(appointment)"
                  (click)="openAppointmentForEdit(appointment)"
                >
                  <!-- Conteúdo do card -->
                  <div class="h-full flex flex-col justify-between">
                    <!-- Header do card -->
                    <div class="flex-shrink-0">
                      <div
                        class="font-semibold text-gray-900 truncate text-sm lg:text-base mb-1"
                      >
                        {{ appointment.patientName }}
                      </div>
                      <div class="text-xs text-gray-600 mb-2">
                        {{ appointment.startTime }} - {{ appointment.endTime }}
                      </div>
                    </div>

                    <!-- Tags de status e tipo (apenas se houver espaço) -->
                    <div
                      *ngIf="appointment.duration >= 30"
                      class="flex flex-wrap gap-1 mt-auto"
                    >
                      <span
                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                        [ngClass]="[
                          getPatientTypeConfig(appointment.patientType).color,
                          getPatientTypeConfig(appointment.patientType).bgColor
                        ]"
                      >
                        <span class="hidden lg:inline">{{
                          getPatientTypeConfig(appointment.patientType).name
                        }}</span>
                        <span class="lg:hidden">{{
                          getPatientTypeConfig(
                            appointment.patientType
                          ).name.charAt(0)
                        }}</span>
                      </span>
                      <span
                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                        [ngClass]="[
                          getAppointmentStatusConfig(appointment.status).color,
                          getAppointmentStatusConfig(appointment.status).bgColor
                        ]"
                      >
                        <span class="hidden lg:inline">{{
                          getAppointmentStatusConfig(appointment.status).name
                        }}</span>
                        <span class="lg:hidden">{{
                          getAppointmentStatusConfig(
                            appointment.status
                          ).name.charAt(0)
                        }}</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>
          </div>

          <!-- Mensagem quando nenhum dentista está selecionado -->
          <div
            *ngIf="getSelectedDentistsData().length === 0"
            class="flex items-center justify-center h-full"
          >
            <div class="text-center text-gray-500 py-20">
              <div class="text-6xl mb-4">👨‍⚕️</div>
              <h3 class="text-xl font-medium mb-2">
                Nenhum dentista selecionado
              </h3>
              <p class="text-gray-400">
                Selecione pelo menos um dentista no filtro lateral para
                visualizar a agenda
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Grid da agenda - Visualização Semanal -->
      <div
        *ngIf="viewMode === 'week'"
        class="bg-white rounded-lg shadow-sm border border-gray-200 h-full overflow-hidden schedule-grid"
        data-view-mode="week"
      >
        <div class="h-full overflow-auto">
          <div
            class="grid"
            [style.grid-template-columns]="getWeekGridColumns()"
            [style.min-width]="
              getMinWeekGridWidth() > 0 ? getMinWeekGridWidth() + 'px' : '100%'
            "
          >
            <!-- Cabeçalho com horários -->
            <div
              class="sticky top-0 bg-gray-50 border-b border-gray-200 p-1 lg:p-3 text-xs font-medium text-gray-500 text-center z-20"
            >
              <span class="hidden sm:inline">Horário</span>
              <span class="sm:hidden">Hr</span>
            </div>

            <!-- Cabeçalhos dos dias da semana -->
            <div
              *ngFor="let day of weekDays"
              class="sticky top-0 dentist-header border-b border-l border-gray-200 p-1 lg:p-3 text-center z-20"
              [class.bg-blue-100]="day.isToday"
              [class.bg-gray-50]="!day.isToday"
              [class.border-blue-300]="day.isToday"
              [class.shadow-md]="day.isToday"
            >
              <!-- Indicador visual especial para hoje -->
              <div
                *ngIf="day.isToday"
                class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-blue-600"
              ></div>

              <div class="flex flex-col items-center space-y-1 relative">
                <span
                  class="text-xs font-medium"
                  [class.text-blue-700]="day.isToday"
                  [class.text-gray-600]="!day.isToday"
                >
                  {{ day.dayName }}
                </span>

                <!-- Número do dia com destaque especial para hoje -->
                <div class="relative">
                  <span
                    class="text-sm lg:text-lg font-bold relative z-10"
                    [class.text-white]="day.isToday"
                    [class.text-gray-900]="!day.isToday"
                  >
                    {{ day.dayNumber }}
                  </span>

                  <!-- Círculo de destaque para o dia atual -->
                  <div
                    *ngIf="day.isToday"
                    class="absolute inset-0 bg-blue-600 rounded-full flex items-center justify-center transform scale-110 -z-10 shadow-lg"
                  ></div>
                </div>

                <!-- Badge "HOJE" -->
                <div
                  *ngIf="day.isToday"
                  class="text-xs font-bold text-blue-700 bg-blue-200 px-2 py-0.5 rounded-full border border-blue-300"
                >
                  HOJE
                </div>
              </div>
            </div>

            <!-- Linhas de horário para visualização semanal -->
            <ng-container *ngFor="let timeSlot of timeSlots; let i = index">
              <!-- Coluna de horário -->
              <div
                class="border-b border-gray-100 p-1 lg:p-2 text-xs text-gray-500 text-center bg-gray-50 sticky left-0 z-10"
                [class.border-r]="true"
                [class.border-gray-300]="timeSlot.minute === 0"
                [class.bg-gray-100]="timeSlot.minute === 0"
              >
                <span
                  class="font-medium text-xs lg:text-sm"
                  [class.text-gray-900]="timeSlot.minute === 0"
                  >{{ timeSlot.time }}</span
                >
              </div>

              <!-- Células para cada dia da semana -->
              <div
                *ngFor="let day of weekDays"
                class="relative min-h-[30px] lg:min-h-[40px] time-slot"
                [class.border-t]="
                  !isWeekSlotOccupiedByAppointment(day.date, timeSlot.time)
                "
                [class.border-b]="
                  !isWeekSlotOccupiedByAppointment(day.date, timeSlot.time)
                "
                [class.border-l]="
                  !isWeekSlotOccupiedByAppointment(day.date, timeSlot.time)
                "
                [class.border-gray-100]="
                  !isWeekSlotOccupiedByAppointment(day.date, timeSlot.time)
                "
                [class.border-gray-300]="
                  timeSlot.minute === 0 &&
                  !isWeekSlotOccupiedByAppointment(day.date, timeSlot.time)
                "
                [class.bg-blue-50]="
                  day.isToday &&
                  !isWeekSlotOccupiedByAppointment(day.date, timeSlot.time)
                "
                [class.occupied-slot]="
                  isWeekSlotOccupiedByAppointment(day.date, timeSlot.time)
                "
                [ngClass]="getWeekSlotOccupiedClasses(day.date, timeSlot.time)"
              >
                <!-- Cards de agendamentos que começam neste slot -->
                <ng-container
                  *ngFor="
                    let appointment of getAppointmentsStartingInWeekSlot(
                      day.date,
                      timeSlot.time
                    );
                    let i = index
                  "
                >
                  <div
                    class="absolute top-0 bottom-0 rounded-md shadow-lg border-l-4 p-1 lg:p-2 text-xs z-10 appointment-card cursor-pointer transition-all duration-200"
                    [style.left.%]="
                      getWeekAppointmentLeftPosition(
                        i,
                        getAppointmentsStartingInWeekSlot(
                          day.date,
                          timeSlot.time
                        ).length
                      )
                    "
                    [style.right.%]="
                      getWeekAppointmentRightPosition(
                        i,
                        getAppointmentsStartingInWeekSlot(
                          day.date,
                          timeSlot.time
                        ).length
                      )
                    "
                    [style.height.px]="getAppointmentHeight(appointment)"
                    [style.background-color]="
                      appointment.appointmentCategory?.color ||
                      getPatientTypeConfig(appointment.patientType).bgColor
                    "
                    [ngClass]="[
                      'border-l-' + getStatusBorderColor(appointment.status)
                    ]"
                    [title]="getAppointmentTooltip(appointment)"
                    (click)="openAppointmentForEdit(appointment)"
                  >
                    <!-- Conteúdo do card -->
                    <div class="h-full flex flex-col justify-between">
                      <!-- Header do card -->
                      <div class="flex-shrink-0">
                        <div class="font-semibold truncate text-xs">
                          {{ appointment.patientName }}
                        </div>
                        <div class="text-xs mb-1">
                          {{ appointment.startTime }} -
                          {{ appointment.endTime }}
                        </div>
                        <div class="text-xs truncate">
                          {{ getDentistName(appointment.dentistId) }}
                        </div>
                      </div>

                      <!-- Tags de status e tipo (apenas se houver espaço suficiente) -->
                      <div
                        *ngIf="
                          appointment.duration >= 30 &&
                          getAppointmentsStartingInWeekSlot(
                            day.date,
                            timeSlot.time
                          ).length <= 2
                        "
                        class="flex flex-wrap gap-1 mt-auto"
                      >
                        <span
                          class="inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium"
                          [ngClass]="[
                            getPatientTypeConfig(appointment.patientType).color,
                            getPatientTypeConfig(appointment.patientType)
                              .bgColor
                          ]"
                        >
                          <span class="hidden lg:inline">{{
                            getPatientTypeConfig(
                              appointment.patientType
                            ).name.charAt(0)
                          }}</span>
                          <span class="lg:hidden">{{
                            getPatientTypeConfig(
                              appointment.patientType
                            ).name.charAt(0)
                          }}</span>
                        </span>
                        <span
                          class="inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium"
                          [ngClass]="[
                            getAppointmentStatusConfig(appointment.status)
                              .color,
                            getAppointmentStatusConfig(appointment.status)
                              .bgColor
                          ]"
                        >
                          <span class="hidden lg:inline">{{
                            getAppointmentStatusConfig(
                              appointment.status
                            ).name.charAt(0)
                          }}</span>
                          <span class="lg:hidden">{{
                            getAppointmentStatusConfig(
                              appointment.status
                            ).name.charAt(0)
                          }}</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
            </ng-container>
          </div>
        </div>
      </div>

      <!-- Visualização Mensal -->
      <div
        *ngIf="viewMode === 'month'"
        class="bg-white rounded-lg shadow-sm border border-gray-200 h-full overflow-hidden"
      >
        <div class="h-full overflow-auto">
          <!-- Cabeçalho dos dias da semana -->
          <div class="grid grid-cols-7 border-b border-gray-200 bg-gray-50">
            <div
              *ngFor="let dayName of dayNames"
              class="p-1 lg:p-3 text-center text-xs lg:text-sm font-medium text-gray-600 border-r border-gray-200 last:border-r-0"
            >
              <span class="hidden sm:inline">{{ dayName }}</span>
              <span class="sm:hidden">{{ dayName.charAt(0) }}</span>
            </div>
          </div>

          <!-- Grid do calendário -->
          <div class="grid grid-cols-7 h-full">
            <div
              *ngFor="let day of monthDays"
              class="relative border-r border-b border-gray-100 min-h-[80px] sm:min-h-[100px] lg:min-h-[120px] p-1 lg:p-2 last:border-r-0"
              [class.bg-gray-50]="!day.isCurrentMonth"
              [class.bg-blue-50]="day.isToday"
              [class.border-blue-200]="day.isToday"
            >
              <!-- Número do dia -->
              <div class="flex justify-between items-start mb-2">
                <span
                  class="text-sm font-medium"
                  [class.text-gray-400]="!day.isCurrentMonth"
                  [class.text-blue-600]="day.isToday"
                  [class.text-gray-900]="day.isCurrentMonth && !day.isToday"
                >
                  {{ day.dayNumber }}
                </span>

                <!-- Indicador de hoje -->
                <div
                  *ngIf="day.isToday"
                  class="w-2 h-2 bg-blue-500 rounded-full"
                ></div>
              </div>

              <!-- Lista de agendamentos -->
              <div class="space-y-1">
                <!-- Agendamentos visíveis (máx. 3) -->
                <div
                  *ngFor="let appointment of getVisibleAppointments(day)"
                  class="text-xs p-1 rounded border-l-2 truncate"
                  [style.background-color]="
                    appointment.appointmentCategory?.color ||
                    getPatientTypeConfig(appointment.patientType).bgColor
                  "
                  [ngClass]="[
                    'border-l-' + getStatusBorderColor(appointment.status)
                  ]"
                  [title]="getAppointmentTooltip(appointment)"
                >
                  <div class="font-medium text-gray-900 truncate">
                    {{ appointment.patientName }}
                  </div>
                  <div class="text-gray-600 truncate">
                    {{ appointment.startTime }}
                  </div>
                </div>

                <!-- Indicador de agendamentos ocultos -->
                <div
                  *ngIf="getHiddenAppointmentsCount(day) > 0"
                  class="text-xs text-gray-500 font-medium p-1 bg-gray-100 rounded text-center"
                >
                  +{{ getHiddenAppointmentsCount(day) }} mais
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Sheet superior para novo agendamento -->
<div
  *ngIf="isSheetOpen || isSheetAnimating"
  class="fixed inset-0 z-50 overflow-hidden"
>
  <!-- Overlay -->
  <div
    class="absolute inset-0 bg-black sheet-overlay"
    [class.bg-opacity-50]="isSheetOpen"
    [class.bg-opacity-0]="!isSheetOpen"
    (click)="closeSheet()"
  ></div>

  <!-- Sheet -->
  <div
    class="absolute rounded-lg top-0 left-1/2 transform -translate-x-1/2 bg-white shadow-xl sheet-panel flex flex-col"
    [class.h-[90vh]]="!isMobile"
    [class.h-full]="isMobile"
    [class.w-[80%]]="!isMobile"
    [class.max-w-[1400px]]="!isMobile"
    [class.w-full]="isMobile"
    [class.translate-y-0]="isSheetOpen"
    [class.-translate-y-full]="!isSheetOpen"
    style="min-width: 320px"
  >
    <!-- Header do Sheet -->
    <div
      class="flex items-center justify-between px-6 py-3 border-b border-gray-200 flex-shrink-0 bg-gray-50"
    >
      <div>
        <h3 class="text-xl font-semibold text-gray-900">
          {{ isEditingAppointment ? "Editar Agendamento" : "Novo Agendamento" }}
        </h3>
        <p
          *ngIf="isEditingAppointment && selectedAppointment"
          class="text-sm text-gray-600 mt-1"
        >
          {{ selectedAppointment.patientName }} -
          {{ selectedAppointment.startTime }} às
          {{ selectedAppointment.endTime }}
        </p>
      </div>
      <button
        (click)="closeSheet()"
        class="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-2 hover:bg-gray-200 rounded-full"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <!-- Conteúdo do Sheet -->
    <div class="flex flex-col flex-1 min-h-0">
      <!-- Componente de formulário de agendamento -->
      <div class="flex-1 overflow-y-auto p-6" style="min-height: 0">
        <app-appointment-form
          [appointment]="selectedAppointment"
          [isEditMode]="isEditingAppointment"
          [initialDate]="getInitialFormDate()"
          [initialTime]="getInitialFormTime()"
          [initialDentistId]="getInitialFormDentistId()"
          [showSubmitButtons]="false"
          (formSubmit)="onAppointmentFormSubmit($event)"
          (formCancel)="closeSheet()"
          (formValid)="onFormValidityChange($event)"
        ></app-appointment-form>
      </div>

      <!-- Footer com botões fixos -->
      <div
        class="border-t rounded-b-lg border-gray-200 px-6 py-3 flex-shrink-0 bg-gray-50"
      >
        <div class="flex flex-col sm:flex-row gap-4 sm:justify-end">
          <button
            type="button"
            (click)="closeSheet()"
            class="w-full sm:w-auto px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 shadow-sm"
          >
            <svg
              class="w-4 h-4 inline mr-2"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
            Cancelar
          </button>
          <button
            type="button"
            (click)="submitAppointmentForm()"
            [disabled]="!isAppointmentFormValid"
            class="w-full flex items-center sm:w-auto px-6 py-3 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm"
          >
            <svg
              class="w-4 h-4 mr-2"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M5 13l4 4L19 7"
              />
            </svg>
            {{
              isEditingAppointment
                ? "Atualizar agendamento"
                : "Criar Agendamento"
            }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Componente de histórico de observações -->
<app-appointment-observations-history
  [patientId]="selectedPatientIdForHistory"
  [isOpen]="isObservationsHistoryOpen"
  (close)="closeObservationsHistory()"
></app-appointment-observations-history>
