<div class="color-select-container w-full">
  <!-- Label -->
  <label *ngIf="label" class="block text-sm font-medium text-gray-700 mb-2">
    {{ label }}
    <span *ngIf="required" class="text-red-500 ml-1">*</span>
  </label>

  <!-- Select Button -->
  <div class="relative w-full">
    <button
      type="button"
      [disabled]="disabled"
      (click)="toggleDropdown()"
      class="relative w-full h-12 cursor-pointer rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-left shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500 transition-all duration-200"
      [class.border-red-300]="hasError"
    >
      <div class="flex items-center h-full">
        <!-- Color Preview -->
        <div
          class="h-6 w-6 rounded-full border-2 border-gray-200 mr-3 flex-shrink-0 shadow-sm"
          [style.background-color]="selectedColorHex"
        ></div>

        <!-- Color Name -->
        <span
          class="block truncate flex-1 min-w-0 text-base"
          [class.text-gray-500]="!selectedColor"
        >
          {{ selectedColorName }}
        </span>
      </div>

      <!-- Arrow Icon -->
      <span
        class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3"
      >
        <svg
          class="h-5 w-5 text-gray-400 transition-transform duration-200"
          [class.rotate-180]="isOpen"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </span>
    </button>

    <!-- Dropdown -->
    <div
      *ngIf="isOpen"
      class="absolute z-50 mt-1 w-full rounded-md bg-white shadow-lg border border-gray-200 max-h-60 overflow-y-auto"
    >
      <div class="py-1">
        <button
          type="button"
          *ngFor="let color of colors; trackBy: trackByColorId"
          (click)="selectColor(color)"
          class="w-full text-left px-3 py-2 hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors duration-150 flex items-center"
          [class.bg-blue-50]="selectedColor?.id === color.id"
        >
          <!-- Color Preview -->
          <div
            class="h-5 w-5 rounded-full border-2 border-gray-200 mr-3 flex-shrink-0 shadow-sm"
            [style.background-color]="color.hex"
          ></div>

          <!-- Color Name -->
          <span class="text-gray-900 text-base">{{ color.name }}</span>

          <!-- Selected indicator -->
          <svg
            *ngIf="selectedColor?.id === color.id"
            class="h-4 w-4 text-blue-600 ml-auto"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clip-rule="evenodd"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Backdrop -->
    <div
      *ngIf="isOpen"
      class="fixed inset-0 z-40"
      (click)="closeDropdown()"
    ></div>
  </div>

  <!-- Validation Error -->
  <p *ngIf="hasError" class="mt-2 text-sm text-red-600">
    {{ errorMessage }}
  </p>
</div>
