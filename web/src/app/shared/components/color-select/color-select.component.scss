/* Estilos para o color select */

.color-select-container {
  position: relative;
  width: 100%;
}

/* Animação para o dropdown */
.absolute.z-50 {
  animation: slideDown 0.15s ease-out;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar personalizada para o dropdown */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.4) transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.4);
    border-radius: 3px;

    &:hover {
      background-color: rgba(156, 163, 175, 0.6);
    }
  }
}

/* Transições suaves */
button {
  transition: all 0.2s ease-in-out;

  &:hover:not(:disabled) {
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px #3b82f6;
  }

  &:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

/* Responsividade */
@media (max-width: 640px) {
  .color-select-container button {
    min-height: 48px; /* Maior área de toque em mobile */
  }

  .absolute.z-50 button {
    min-height: 44px;
    padding: 0.75rem;
  }
}
