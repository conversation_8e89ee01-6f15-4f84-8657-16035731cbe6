import {
  Component,
  Input,
  Output,
  EventEmitter,
  forwardRef,
  HostListener,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

export interface ColorOption {
  id: number;
  name: string;
  hex: string;
}

@Component({
  selector: 'app-color-select',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './color-select.component.html',
  styleUrl: './color-select.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ColorSelectComponent),
      multi: true,
    },
  ],
})
export class ColorSelectComponent implements ControlValueAccessor {
  @Input() colors: ColorOption[] = [];
  @Input() placeholder = 'Selecione uma cor';
  @Input() disabled = false;
  @Input() required = false;
  @Input() label = '';
  @Input() errorMessage = 'Este campo é obrigatório';

  @Output() colorChange = new EventEmitter<ColorOption | null>();

  selectedColor: ColorOption | null = null;
  isOpen = false;
  private touched = false;

  // ControlValueAccessor implementation
  private onChange = (value: any) => {};
  private onTouched = () => {};

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    // Fecha o dropdown se clicar fora do componente
    const target = event.target as HTMLElement;
    const colorSelectContainer = target.closest('.color-select-container');
    if (!colorSelectContainer && this.isOpen) {
      this.closeDropdown();
    }
  }

  writeValue(value: any): void {
    if (value) {
      this.selectedColor =
        this.colors.find((color) => color.hex === value) || null;
    } else {
      this.selectedColor = null;
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  toggleDropdown(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (!this.disabled) {
      this.isOpen = !this.isOpen;
      if (!this.touched) {
        this.touched = true;
        this.onTouched();
      }
    }
  }

  selectColor(color: ColorOption, event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    this.selectedColor = color;
    this.isOpen = false;
    this.onChange(color.hex);
    this.colorChange.emit(color);

    if (!this.touched) {
      this.touched = true;
      this.onTouched();
    }
  }

  closeDropdown(): void {
    this.isOpen = false;
  }

  get selectedColorName(): string {
    return this.selectedColor?.name || this.placeholder;
  }

  get selectedColorHex(): string {
    return this.selectedColor?.hex || '#e5e7eb';
  }

  get hasError(): boolean {
    return this.required && !this.selectedColor && this.touched;
  }

  trackByColorId(index: number, color: ColorOption): number {
    return color.id;
  }
}
