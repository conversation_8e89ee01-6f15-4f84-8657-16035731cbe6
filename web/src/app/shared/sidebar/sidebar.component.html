<aside
  class="h-screen w-full flex flex-col bg-white shadow-sm overflow-y-auto scrollbar-thin transition-all duration-300 ease-in-out"
  [ngClass]="{
    'w-64': !isCollapsed,
    'w-20': isCollapsed
  }"
>
  <!-- Logo -->
  <div class="px-2 py-4" [ngClass]="{ 'py-6 pt-4 w-full': isCollapsed }">
    <a routerLink="/" class="flex items-center justify-center w-full">
      <!-- Logo completo quando expandido -->
      <img
        *ngIf="!isCollapsed"
        src="assets/logo-moderno.svg"
        alt="Logo CRM Odonto"
        class="h-32 transition-opacity duration-300"
      />
      <!-- Logo colapsado (apenas sigla CRM) -->
      <div
        *ngIf="isCollapsed"
        class="w-16 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center text-white font-bold text-base shadow-lg transition-all duration-300 hover:shadow-xl"
      >
        CRM
      </div>
    </a>
  </div>

  <!-- <PERSON><PERSON> de navegação -->
  <nav
    class="flex-1 py-2 overflow-y-auto"
    [ngClass]="{ 'px-4': !isCollapsed, 'px-2': isCollapsed }"
  >
    <ul class="space-y-1.5">
      <li>
        <a
          routerLink="/"
          routerLinkActive="bg-blue-100 text-blue-600"
          [routerLinkActiveOptions]="{ exact: true }"
          class="flex items-center rounded-lg hover:bg-blue-50 transition-colors relative group"
          [ngClass]="{
            'p-2.5': !isCollapsed,
            'p-3 justify-center': isCollapsed
          }"
          [title]="isCollapsed ? 'Dashboard' : ''"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 transition-all duration-300"
            [ngClass]="{ 'mr-3': !isCollapsed }"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"
            />
          </svg>
          <span *ngIf="!isCollapsed" class="transition-opacity duration-300"
            >Dashboard</span
          >

          <!-- Tooltip para modo colapsado -->
          <div
            *ngIf="isCollapsed"
            class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50"
          >
            Dashboard
          </div>
        </a>
      </li>

      <!-- Board de Atendimentos -->
      <li>
        <a
          routerLink="/schedulings/board"
          routerLinkActive="bg-blue-100 text-blue-600"
          class="flex items-center rounded-lg hover:bg-blue-50 transition-colors relative group"
          [ngClass]="{
            'p-2.5': !isCollapsed,
            'p-3 justify-center': isCollapsed
          }"
          [title]="isCollapsed ? 'Board de Atendimentos' : ''"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 transition-all duration-300"
            [ngClass]="{ 'mr-3': !isCollapsed }"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
            />
          </svg>
          <span *ngIf="!isCollapsed" class="transition-opacity duration-300"
            >Board de Atendimentos</span
          >

          <!-- Tooltip para modo colapsado -->
          <div
            *ngIf="isCollapsed"
            class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50"
          >
            Board de Atendimentos
          </div>
        </a>
      </li>

      <!-- Demandas -->
      <li>
        <a
          routerLink="/tasks"
          routerLinkActive="bg-blue-100 text-blue-600"
          class="flex items-center rounded-lg hover:bg-blue-50 transition-colors relative group"
          [ngClass]="{
            'p-2.5': !isCollapsed,
            'p-3 justify-center': isCollapsed
          }"
          [title]="isCollapsed ? 'Board de Demandas' : ''"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 transition-all duration-300"
            [ngClass]="{ 'mr-3': !isCollapsed }"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
              clip-rule="evenodd"
            />
          </svg>
          <span *ngIf="!isCollapsed" class="transition-opacity duration-300"
            >Board de Demandas</span
          >

          <!-- Tooltip para modo colapsado -->
          <div
            *ngIf="isCollapsed"
            class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50"
          >
            Board de Demandas
          </div>
        </a>
      </li>

      <!-- Menu Gestão Clínica -->
      <li class="collapsed-submenu-container relative">
        <div
          (click)="toggleClinicalManagement($event)"
          class="flex items-center rounded-lg hover:bg-blue-50 transition-colors cursor-pointer relative group"
          [ngClass]="{
            'bg-blue-50 text-blue-600':
              clinicalManagementExpanded || isCollapsedSubmenuOpen('clinical'),
            'p-2.5 justify-between': !isCollapsed,
            'p-3 justify-center': isCollapsed
          }"
          [title]="isCollapsed ? 'Gestão Clínica' : ''"
        >
          <div class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 transition-all duration-300"
              [ngClass]="{ 'mr-3': !isCollapsed }"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                clip-rule="evenodd"
              />
            </svg>
            <span *ngIf="!isCollapsed" class="transition-opacity duration-300"
              >Gestão Clínica</span
            >
          </div>
          <svg
            *ngIf="!isCollapsed"
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 transition-transform"
            [ngClass]="{ 'rotate-180': clinicalManagementExpanded }"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>

          <!-- Tooltip para modo colapsado (apenas quando submenu não está aberto) -->
          <div
            *ngIf="isCollapsed && !isCollapsedSubmenuOpen('clinical')"
            class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50"
          >
            Gestão Clínica
          </div>
        </div>

        <!-- Submenu Dropdown para modo colapsado -->
        <div
          *ngIf="isCollapsedSubmenuOpen('clinical')"
          class="fixed left-20 bg-white shadow-xl rounded-lg border border-gray-200 py-2 min-w-[200px] max-w-[280px] w-auto h-auto max-h-[calc(100vh-40px)] overflow-y-auto animate-in"
          style="z-index: 9999 !important"
          #clinicalDropdown
        >
          <div
            class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100"
          >
            Gestão Clínica
          </div>

          <!-- Pacientes -->
          <a
            routerLink="/patients"
            routerLinkActive="bg-blue-50 text-blue-600"
            class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
            (click)="collapsedSubmenuOpen = null"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"
              />
            </svg>
            Pacientes
          </a>

          <!-- Agendamentos -->
          <a
            routerLink="/schedulings"
            routerLinkActive="bg-blue-50 text-blue-600"
            [routerLinkActiveOptions]="{ exact: true }"
            class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
            (click)="collapsedSubmenuOpen = null"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                clip-rule="evenodd"
              />
            </svg>
            Agendamentos
          </a>

          <!-- Prontuários -->
          <a
            routerLink="/medical-records"
            routerLinkActive="bg-blue-50 text-blue-600"
            class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
            (click)="collapsedSubmenuOpen = null"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                clip-rule="evenodd"
              />
            </svg>
            Prontuários
          </a>

          <!-- Agenda -->
          <a
            routerLink="/schedule"
            routerLinkActive="bg-blue-50 text-blue-600"
            class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
            (click)="collapsedSubmenuOpen = null"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                clip-rule="evenodd"
              />
            </svg>
            Agenda
          </a>

          <!-- Schedule v2 -->
          <a
            routerLink="/schedule-v2"
            routerLinkActive="bg-blue-50 text-blue-600"
            class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
            (click)="collapsedSubmenuOpen = null"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                clip-rule="evenodd"
              />
            </svg>
            <span>Agenda v2</span>
            <span
              class="ml-auto inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800"
              >Novo</span
            >
          </a>

          <!-- Dentistas -->
          <a
            routerLink="/dentists"
            routerLinkActive="bg-blue-50 text-blue-600"
            class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
            (click)="collapsedSubmenuOpen = null"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                clip-rule="evenodd"
              />
            </svg>
            Dentistas
          </a>

          <!-- Escalas de Dentistas -->
          <a
            routerLink="/dentist-schedules"
            routerLinkActive="bg-blue-50 text-blue-600"
            class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
            (click)="collapsedSubmenuOpen = null"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                clip-rule="evenodd"
              />
            </svg>
            Escalas de Dentistas
          </a>

          <!-- CRC -->
          <a
            routerLink="/crc"
            routerLinkActive="bg-blue-50 text-blue-600"
            class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
            (click)="collapsedSubmenuOpen = null"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"
                clip-rule="evenodd"
              />
            </svg>
            CRC
          </a>

          <!-- Funcionários -->
          <a
            routerLink="/employees"
            routerLinkActive="bg-blue-50 text-blue-600"
            class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
            (click)="collapsedSubmenuOpen = null"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"
              />
            </svg>
            Funcionários
          </a>
        </div>

        <!-- Submenu -->
        <div *ngIf="clinicalManagementExpanded" class="ml-6 mt-2 space-y-1.5">
          <!-- Pacientes -->
          <a
            routerLink="/patients"
            routerLinkActive="bg-blue-100 text-blue-600"
            class="flex items-center p-2 rounded-lg hover:bg-blue-50 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"
              />
            </svg>
            <span>Pacientes</span>
          </a>

          <!-- Agendamentos -->
          <a
            routerLink="/schedulings"
            routerLinkActive="bg-blue-100 text-blue-600"
            [routerLinkActiveOptions]="{ exact: true }"
            class="flex items-center p-2 rounded-lg hover:bg-blue-50 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                clip-rule="evenodd"
              />
            </svg>
            <span>Agendamentos</span>
          </a>

          <!-- Prontuários -->
          <a
            routerLink="/medical-records"
            routerLinkActive="bg-blue-100 text-blue-600"
            class="flex items-center p-2 rounded-lg hover:bg-blue-50 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                clip-rule="evenodd"
              />
            </svg>
            <span>Prontuários</span>
          </a>

          <!-- Agenda -->
          <a
            routerLink="/schedule"
            routerLinkActive="bg-blue-100 text-blue-600"
            class="flex items-center p-2 rounded-lg hover:bg-blue-50 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                clip-rule="evenodd"
              />
            </svg>
            <span>Agenda</span>
          </a>

          <!-- Schedule v2 -->
          <a
            routerLink="/schedule-v2"
            routerLinkActive="bg-blue-100 text-blue-600"
            class="flex items-center p-2 rounded-lg hover:bg-blue-50 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                clip-rule="evenodd"
              />
            </svg>
            <span>Agenda v2</span>
            <span
              class="ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800"
            >
              Novo
            </span>
          </a>

          <!-- Dentistas -->
          <a
            routerLink="/dentists"
            routerLinkActive="bg-blue-100 text-blue-600"
            class="flex items-center p-2 rounded-lg hover:bg-blue-50 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                clip-rule="evenodd"
              />
            </svg>
            <span>Dentistas</span>
          </a>

          <!-- Escalas de Dentistas -->
          <a
            routerLink="/dentist-schedules"
            routerLinkActive="bg-blue-100 text-blue-600"
            class="flex items-center p-2 rounded-lg hover:bg-blue-50 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                clip-rule="evenodd"
              />
            </svg>
            <span>Escalas de Dentistas</span>
          </a>

          <!-- CRC -->
          <a
            routerLink="/crc"
            routerLinkActive="bg-blue-100 text-blue-600"
            class="flex items-center p-2 rounded-lg hover:bg-blue-50 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"
                clip-rule="evenodd"
              />
            </svg>
            <span>CRC</span>
          </a>

          <!-- Funcionários -->
          <a
            routerLink="/employees"
            routerLinkActive="bg-blue-100 text-blue-600"
            class="flex items-center p-2 rounded-lg hover:bg-blue-50 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"
              />
            </svg>
            <span>Funcionários</span>
          </a>
        </div>
      </li>

      <!-- Relatórios -->
      <li>
        <a
          routerLink="/reports"
          routerLinkActive="bg-blue-100 text-blue-600"
          class="flex items-center rounded-lg hover:bg-blue-50 transition-colors relative group"
          [ngClass]="{
            'p-2.5': !isCollapsed,
            'p-3 justify-center': isCollapsed
          }"
          [title]="isCollapsed ? 'Relatórios' : ''"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 transition-all duration-300"
            [ngClass]="{ 'mr-3': !isCollapsed }"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
              clip-rule="evenodd"
            />
          </svg>
          <span *ngIf="!isCollapsed" class="transition-opacity duration-300"
            >Relatórios</span
          >

          <!-- Tooltip para modo colapsado -->
          <div
            *ngIf="isCollapsed"
            class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50"
          >
            Relatórios
          </div>
        </a>
      </li>

      <!-- Leads -->
      <li>
        <a
          routerLink="/leads"
          routerLinkActive="bg-blue-100 text-blue-600"
          class="flex items-center rounded-lg hover:bg-blue-50 transition-colors relative group"
          [ngClass]="{
            'p-2.5': !isCollapsed,
            'p-3 justify-center': isCollapsed
          }"
          [title]="isCollapsed ? 'Gestão de Leads' : ''"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 transition-all duration-300"
            [ngClass]="{ 'mr-3': !isCollapsed }"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              d="M10.3 1a4.3 4.3 0 00-4.292 4.291v1.927H4.3a2.3 2.3 0 00-2.3 2.3v8.182a2.3 2.3 0 002.3 2.3h12a2.3 2.3 0 002.3-2.3V9.518a2.3 2.3 0 00-2.3-2.3h-1.709V5.291A4.3 4.3 0 0010.3 1zm2.091 6.218H8.209V5.291a2.091 2.091 0 114.182 0v1.927z"
            />
          </svg>
          <span *ngIf="!isCollapsed" class="transition-opacity duration-300"
            >Gestão de Leads</span
          >

          <!-- Tooltip para modo colapsado -->
          <div
            *ngIf="isCollapsed"
            class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50"
          >
            Gestão de Leads
          </div>
        </a>
      </li>

      <!-- Board de Sugestões da IA -->
      <li>
        <a
          routerLink="/ai-suggestions"
          routerLinkActive="bg-blue-100 text-blue-600"
          class="flex items-center rounded-lg hover:bg-blue-50 transition-colors relative group"
          [ngClass]="{
            'p-2.5': !isCollapsed,
            'p-3 justify-center': isCollapsed
          }"
          [title]="isCollapsed ? 'Board de Sugestões da IA' : ''"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 transition-all duration-300"
            [ngClass]="{ 'mr-3': !isCollapsed }"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"
            />
            <path
              fill-rule="evenodd"
              d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"
              clip-rule="evenodd"
            />
          </svg>
          <span *ngIf="!isCollapsed" class="transition-opacity duration-300"
            >Board de Sugestões da IA</span
          >

          <!-- Tooltip para modo colapsado -->
          <div
            *ngIf="isCollapsed"
            class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50"
          >
            Board de Sugestões da IA
          </div>
        </a>
      </li>

      <!-- Menu Configurações -->
      <li class="collapsed-submenu-container relative">
        <div
          (click)="toggleSettings($event)"
          class="flex items-center rounded-lg hover:bg-blue-50 transition-colors cursor-pointer relative group"
          [ngClass]="{
            'bg-blue-50 text-blue-600':
              settingsExpanded || isCollapsedSubmenuOpen('settings'),
            'p-2.5 justify-between': !isCollapsed,
            'p-3 justify-center': isCollapsed
          }"
          [title]="isCollapsed ? 'Configurações' : ''"
        >
          <div class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 transition-all duration-300"
              [ngClass]="{ 'mr-3': !isCollapsed }"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                clip-rule="evenodd"
              />
            </svg>
            <span *ngIf="!isCollapsed" class="transition-opacity duration-300"
              >Configurações</span
            >
          </div>
          <svg
            *ngIf="!isCollapsed"
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 transition-transform"
            [ngClass]="{ 'rotate-180': settingsExpanded }"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>

          <!-- Tooltip para modo colapsado (apenas quando submenu não está aberto) -->
          <div
            *ngIf="isCollapsed && !isCollapsedSubmenuOpen('settings')"
            class="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50"
          >
            Configurações
          </div>
        </div>

        <!-- Submenu Dropdown para modo colapsado -->
        <div
          *ngIf="isCollapsedSubmenuOpen('settings')"
          class="fixed left-20 bg-white shadow-xl rounded-lg border border-gray-200 py-2 min-w-[200px] max-w-[280px] w-auto h-auto max-h-[calc(100vh-40px)] overflow-y-auto animate-in"
          style="z-index: 9999 !important"
          #settingsDropdown
        >
          <div
            class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100"
          >
            Configurações
          </div>

          <!-- Tipos de Paciente -->
          <a
            routerLink="/settings/patient-types"
            routerLinkActive="bg-blue-50 text-blue-600"
            class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
            (click)="collapsedSubmenuOpen = null"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"
              />
            </svg>
            Tipos de Paciente
          </a>

          <!-- Procedimentos -->
          <a
            routerLink="/settings/procedures"
            routerLinkActive="bg-blue-50 text-blue-600"
            class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
            (click)="collapsedSubmenuOpen = null"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-3"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path
                d="M20 6h-4V4c0-1.1-.9-2-2-2h-4c-1.1 0-2 .9-2 2v2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-8 11c-.55 0-1-.45-1-1v-2h-2c-.55 0-1-.45-1-1s.45-1 1-1h2v-2c0-.55.45-1 1-1s1 .45 1 1v2h2c.55 0 1 .45 1 1s-.45 1-1 1h-2v2c0 .55-.45 1-1 1zm2-13V4h-4v2h4z"
              />
            </svg>
            Procedimentos
          </a>

          <!-- Fluxo de Tratamento -->
          <a
            routerLink="/settings/treatment-flow"
            routerLinkActive="bg-blue-50 text-blue-600"
            class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
            (click)="collapsedSubmenuOpen = null"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-3"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path
                d="M4 15h16c.55 0 1-.45 1-1s-.45-1-1-1H4c-.55 0-1 .45-1 1s.45 1 1 1zm0 4h16c.55 0 1-.45 1-1s-.45-1-1-1H4c-.55 0-1 .45-1 1s.45 1 1 1zm0-8h16c.55 0 1-.45 1-1s-.45-1-1-1H4c-.55 0-1 .45-1 1s.45 1 1 1zM3 6c0 .55.45 1 1 1h16c.55 0 1-.45 1-1s-.45-1-1-1H4c-.55 0-1 .45-1 1z"
              />
            </svg>
            Fluxo de Tratamento
          </a>

          <!-- Categorias de Agendamento -->
          <a
            routerLink="/settings/appointment-categories"
            routerLinkActive="bg-blue-50 text-blue-600"
            class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
            (click)="collapsedSubmenuOpen = null"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-3"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path
                d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"
              />
            </svg>
            Categorias de Agendamento
          </a>
        </div>

        <!-- Submenu Configurações -->
        <div *ngIf="settingsExpanded" class="ml-6 mt-2 space-y-1.5">
          <!-- Tipos de Paciente -->
          <a
            routerLink="/settings/patient-types"
            routerLinkActive="bg-blue-100 text-blue-600"
            class="flex items-center p-2 rounded-lg hover:bg-blue-50 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"
              />
            </svg>
            <span>Classificação de Paciente</span>
          </a>

          <!-- Procedimentos -->
          <a
            routerLink="/settings/procedures"
            routerLinkActive="bg-blue-100 text-blue-600"
            class="flex items-center p-2 rounded-lg hover:bg-blue-50 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-3"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path
                d="M20 6h-4V4c0-1.1-.9-2-2-2h-4c-1.1 0-2 .9-2 2v2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-8 11c-.55 0-1-.45-1-1v-2h-2c-.55 0-1-.45-1-1s.45-1 1-1h2v-2c0-.55.45-1 1-1s1 .45 1 1v2h2c.55 0 1 .45 1 1s-.45 1-1 1h-2v2c0 .55-.45 1-1 1zm2-13V4h-4v2h4z"
              />
            </svg>
            <span>Procedimentos</span>
          </a>

          <!-- Fluxo de Tratamento -->
          <a
            routerLink="/settings/treatment-flow"
            routerLinkActive="bg-blue-100 text-blue-600"
            class="flex items-center p-2 rounded-lg hover:bg-blue-50 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-3"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path
                d="M4 15h16c.55 0 1-.45 1-1s-.45-1-1-1H4c-.55 0-1 .45-1 1s.45 1 1 1zm0 4h16c.55 0 1-.45 1-1s-.45-1-1-1H4c-.55 0-1 .45-1 1s.45 1 1 1zm0-8h16c.55 0 1-.45 1-1s-.45-1-1-1H4c-.55 0-1 .45-1 1s.45 1 1 1zM3 6c0 .55.45 1 1 1h16c.55 0 1-.45 1-1s-.45-1-1-1H4c-.55 0-1 .45-1 1z"
              />
            </svg>
            <span>Fluxo de Tratamento</span>
          </a>

          <!-- Categorias de Agendamento -->
          <a
            routerLink="/settings/appointment-categories"
            routerLinkActive="bg-blue-100 text-blue-600"
            class="flex items-center p-2 rounded-lg hover:bg-blue-50 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-3"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path
                d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"
              />
            </svg>
            <span>Categorias de Agendamento</span>
          </a>
        </div>
      </li>
    </ul>
  </nav>
</aside>
